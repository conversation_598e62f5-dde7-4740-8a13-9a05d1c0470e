"""
转场效果库 - 提供多种经典视频转场特效
"""

from manim import *
import random
from typing import Optional, Callable, List
from loguru import logger

EPSILON = 1e-6

class TransitionEffects:
    """转场效果类，提供各种经典的视频转场特效"""

    @staticmethod
    def fade_out_in(old_mobj: Mobject, new_mobj: Optional[Mobject] = None,
                    run_time: float = 1.0) -> List[Animation]:
        """经典淡入淡出转场"""
        animations = [FadeOut(old_mobj, run_time=run_time)]
        if new_mobj:
            animations.append(FadeIn(new_mobj, run_time=run_time))
        return animations

    @staticmethod
    def slide_left(old_mobj: Mobject, new_mobj: Optional[Mobject] = None,
                   run_time: float = 1.0) -> List[Animation]:
        """向左滑动转场"""
        animations = [old_mobj.animate.shift(LEFT * config.frame_width)]
        if new_mobj:
            new_mobj.shift(RIGHT * config.frame_width)
            animations.append(new_mobj.animate.shift(LEFT * config.frame_width))
        return animations

    @staticmethod
    def slide_right(old_mobj: Mobject, new_mobj: Optional[Mobject] = None,
                    run_time: float = 1.0) -> List[Animation]:
        """向右滑动转场"""
        animations = [old_mobj.animate.shift(RIGHT * config.frame_width)]
        if new_mobj:
            new_mobj.shift(LEFT * config.frame_width)
            animations.append(new_mobj.animate.shift(RIGHT * config.frame_width))
        return animations

    @staticmethod
    def slide_up(old_mobj: Mobject, new_mobj: Optional[Mobject] = None,
                 run_time: float = 1.0) -> List[Animation]:
        """向上滑动转场"""
        animations = [old_mobj.animate.shift(UP * config.frame_height)]
        if new_mobj:
            new_mobj.shift(DOWN * config.frame_height)
            animations.append(new_mobj.animate.shift(UP * config.frame_height))
        return animations

    @staticmethod
    def slide_down(old_mobj: Mobject, new_mobj: Optional[Mobject] = None,
                   run_time: float = 1.0) -> List[Animation]:
        """向下滑动转场"""
        animations = [old_mobj.animate.shift(DOWN * config.frame_height)]
        if new_mobj:
            new_mobj.shift(UP * config.frame_height)
            animations.append(new_mobj.animate.shift(DOWN * config.frame_height))
        return animations

    @staticmethod
    def zoom_out_in(old_mobj: Mobject, new_mobj: Optional[Mobject] = None,
                    run_time: float = 1.0) -> List[Animation]:
        """缩放转场 - 先缩小消失，再放大出现"""
        animations = [old_mobj.animate.scale(EPSILON).set_opacity(0)]
        if new_mobj:
            new_mobj.scale(EPSILON).set_opacity(0)
            animations.append(new_mobj.animate.scale(1 / EPSILON).set_opacity(1))
        return animations

    @staticmethod
    def rotate_out_in(old_mobj: Mobject, new_mobj: Optional[Mobject] = None,
                      run_time: float = 1.0) -> List[Animation]:
        """旋转转场"""
        animations = [Rotate(old_mobj, angle=PI, run_time=run_time/2),
                     FadeOut(old_mobj, run_time=run_time/2)]
        if new_mobj:
            new_mobj.rotate(-PI)
            animations.extend([FadeIn(new_mobj, run_time=run_time/2),
                              Rotate(new_mobj, angle=PI, run_time=run_time/2)])
        return animations

    @staticmethod
    def wipe_left(old_mobj: Mobject, new_mobj: Optional[Mobject] = None,
                  run_time: float = 1.0) -> List[Animation]:
        """从左到右擦除转场"""
        animations = [old_mobj.animate.shift(LEFT * config.frame_width)]
        if new_mobj:
            animations.append(FadeIn(new_mobj, run_time=run_time))
        return animations

    @staticmethod
    def circle_wipe(old_mobj: Mobject, new_mobj: Optional[Mobject] = None,
                    run_time: float = 1.0) -> List[Animation]:
        """圆形擦除转场"""
        animations = [old_mobj.animate.scale(0.1).set_opacity(0)]
        if new_mobj:
            new_mobj.scale(0.1).set_opacity(0)
            animations.append(new_mobj.animate.scale(10).set_opacity(1))
        return animations

    @staticmethod
    def flip_horizontal(old_mobj: Mobject, new_mobj: Optional[Mobject] = None,
                        run_time: float = 1.0) -> List[Animation]:
        """水平翻转转场"""
        animations = [old_mobj.animate.stretch(0, 0)]  # 水平压缩到0
        if new_mobj:
            new_mobj.stretch(0, 0)  # 初始状态水平压缩
            animations.append(new_mobj.animate.stretch(1, 0))  # 恢复正常
        return animations

    @staticmethod
    def spiral_out(old_mobj: Mobject, new_mobj: Optional[Mobject] = None,
                   run_time: float = 1.0) -> List[Animation]:
        """螺旋消失转场"""
        animations = [
            old_mobj.animate.rotate(2*PI).scale(EPSILON).set_opacity(0)
        ]
        if new_mobj:
            new_mobj.scale(EPSILON).set_opacity(0).rotate(-2*PI)
            animations.append(
                new_mobj.animate.rotate(2*PI).scale(1 / EPSILON).set_opacity(1)
            )
        return animations


class TransitionManager:
    """转场管理器，负责选择和执行转场效果"""

    # 所有可用的转场效果
    TRANSITION_EFFECTS = {
        'fade': TransitionEffects.fade_out_in,
        'slide_left': TransitionEffects.slide_left,
        'slide_right': TransitionEffects.slide_right,
        'slide_up': TransitionEffects.slide_up,
        'slide_down': TransitionEffects.slide_down,
        'zoom': TransitionEffects.zoom_out_in,
        'rotate': TransitionEffects.rotate_out_in,
        'wipe_left': TransitionEffects.wipe_left,
        'circle_wipe': TransitionEffects.circle_wipe,
        'flip': TransitionEffects.flip_horizontal,
        'spiral': TransitionEffects.spiral_out,
    }

    @classmethod
    def get_random_transition(cls) -> str:
        """随机选择一个转场效果"""
        return random.choice(list(cls.TRANSITION_EFFECTS.keys()))

    @classmethod
    def get_transition_function(cls, transition_name: str) -> Callable:
        """根据名称获取转场函数"""
        return cls.TRANSITION_EFFECTS.get(transition_name, TransitionEffects.fade_out_in)

    @classmethod
    def apply_transition(cls, scene, old_mobj: Mobject, new_mobj: Optional[Mobject] = None,
                        transition_type: Optional[str] = None, run_time: float = 1.0):
        """应用转场效果"""
        if not transition_type:
            transition_type = cls.get_random_transition()

        logger.info(f"应用转场效果: {transition_type}")

        transition_func = cls.get_transition_function(transition_type)
        animations = transition_func(old_mobj, new_mobj, run_time)

        if animations:
            scene.play(*animations, run_time=run_time)
