"""
分镜间转场功能 - 基于序列化的场景状态生成转场视频
"""

from manim import *
from pathlib import Path
import pickle
import json
from typing import Optional, Dict, Any
from loguru import logger
from dsl.v2.core.transition_effects import TransitionManager
from dsl.v2.core.scene import FeynmanScene


class InterSceneTransition(FeynmanScene):
    """分镜间转场类，负责生成分镜之间的转场视频"""

    def __init__(self, from_scene_id: str, to_scene_id: str, transition_type: Optional[str] = None, states_dir: str = "output/scene_states"):
        self.states_dir = Path(states_dir)
        self.states_dir.mkdir(parents=True, exist_ok=True)
        self.from_scene_id = from_scene_id
        self.to_scene_id = to_scene_id
        self.transition_type = transition_type
        config.output_file = f"transition_{from_scene_id}_{to_scene_id}_{transition_type}.mp4"
        super().__init__()

    def load_scene_state(self, scene_id: str) -> Optional[Mobject]:
        """加载场景状态"""
        try:
            # 首先尝试加载pickle文件
            state_file = self.states_dir / f"{scene_id}.pkl"
            if state_file.exists():
                with open(state_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.warning(f"加载场景状态失败: {e}")
        return None

    def construct(self):
        self.add_background()
        try:
            # 加载两个分镜的状态
            from_mobj = self.load_scene_state(self.from_scene_id)
            to_mobj = self.load_scene_state(self.to_scene_id)

            if not from_mobj and not to_mobj:
                logger.warning(f"无法找到分镜状态: {self.from_scene_id} -> {self.to_scene_id}")
                return False

            # 选择转场类型
            if not self.transition_type:
                self.transition_type = TransitionManager.get_random_transition()

            # 创建转场场景并渲染
            # 显示源对象
            if from_mobj:
                self.add(from_mobj)
                self.wait(0.5)

            # 应用转场效果
            if from_mobj or to_mobj:
                TransitionManager.apply_transition(
                    scene=self,
                    old_mobj=from_mobj,
                    new_mobj=to_mobj,
                    transition_type=self.transition_type,
                    run_time=1.5
                )
        except Exception as e:
            logger.error(f"渲染转场视频失败: {e}")
            return False

def generate_inter_scene_transition(from_scene_id: str,
                                    to_scene_id: str,
                                    transition_type: Optional[str] = None,
                                ) -> bool:
    """
    便捷函数：直接渲染分镜间转场视频

    Args:
        from_scene_id: 源分镜ID
        to_scene_id: 目标分镜ID
        transition_type: 转场类型
        output_file: 输出视频文件名

    Returns:
        是否成功渲染
    """
    transition = InterSceneTransition(from_scene_id, to_scene_id, transition_type)
    transition.render(preview=True)
    return True
